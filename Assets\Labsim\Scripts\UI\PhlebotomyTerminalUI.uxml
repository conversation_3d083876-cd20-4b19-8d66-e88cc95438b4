<engine:UXML xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:engine="UnityEngine.UIElements" xmlns:editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/Labsim/Scripts/UI/PhlebotomyTerminalUI.uss?fileID=7433441132597879392&amp;guid=0b5a27a9e723ba046a69acc7edb70673&amp;type=3#PhlebotomyTerminalUI" />
    <engine:VisualElement name="MainContainer" class="main-container">
        <engine:VisualElement name="HeaderSection" class="header-section">
            <engine:Label text="Phlebotomy Terminal" name="HeaderLabel" class="header-label" />
        </engine:VisualElement>
        <engine:VisualElement name="ContentArea" class="content-area">
            <engine:VisualElement name="LeftPanel" class="left-panel">
                <engine:VisualElement name="PatientInfoContainer" class="patient-info-container">
                    <engine:Label text="Patient Information" name="PatientInfoHeader" class="section-header" />
                    <engine:Label text="Patient ID: Not Selected" name="PatientIdLabel" class="patient-id-label" />
                    <engine:VisualElement name="PatientStats" class="patient-stats">
                        <engine:Label text="Status: Ready for Testing" name="PatientStatusLabel" class="patient-status" />
                    </engine:VisualElement>
                </engine:VisualElement>
                <engine:VisualElement name="InstructionsPanel" class="instructions-panel">
                    <engine:Label text="Instructions" name="InstructionsHeader" class="section-header" />
                    <engine:Label text="Hello World" name="InstructionsText" class="instructions-text" />
                </engine:VisualElement>
                <engine:VisualElement name="ButtonsPanel" class="buttons-panel">
                    <engine:Button text="Print" name="PrintButton" class="print-button" />
                </engine:VisualElement>
            </engine:VisualElement>
            <engine:VisualElement name="RightPanel" class="right-panel">
                <engine:VisualElement name="TestsContainer" class="tests-container">
                    <engine:Label text="Laboratory Tests" name="TestsHeaderLabel" class="section-header" />
                    <engine:ScrollView name="TestsScrollView" class="tests-scroll-view">
                        <engine:VisualElement name="TestsList" class="tests-list" />
                    </engine:ScrollView>
                </engine:VisualElement>
                <engine:VisualElement name="NoTestsMessage" class="no-tests-message">
                    <engine:Label text="No tests found for this patient.&#10;Scan a patient bracelet to view their laboratory tests." name="NoTestsLabel" class="no-tests-label" />
                </engine:VisualElement>
            </engine:VisualElement>
        </engine:VisualElement>
    </engine:VisualElement>
</engine:UXML>
